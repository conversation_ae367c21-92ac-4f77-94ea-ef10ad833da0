/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 通用样式 */
.btn {
  width: 80%;
  margin: 20rpx 0;
  background-color: #007aff;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  font-size: 32rpx;
}

.btn:active {
  background-color: #0056b3;
}

.color-display {
  width: 200rpx;
  height: 200rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ccc;
  margin: 20rpx 0;
}

.slider-container {
  width: 90%;
  margin: 20rpx 0;
}

.slider-label {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.slider-value {
  color: #666;
  font-weight: bold;
}
