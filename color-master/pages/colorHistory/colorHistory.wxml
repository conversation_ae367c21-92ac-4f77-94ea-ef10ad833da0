<!--pages/colorHistory/colorHistory.wxml-->
<view class="container">
  <view class="header">
    <text class="title">颜色历史</text>
    <text class="subtitle">查看和管理已保存的颜色</text>
  </view>

  <view class="color-list" wx:if="{{colorList.length > 0}}">
    <view class="color-item" wx:for="{{colorList}}" wx:key="timestamp" bindtap="selectColor" data-color="{{item}}">
      <view class="color-preview" style="background-color: {{item.rgb}};"></view>
      <view class="color-details">
        <view class="color-values">
          <text class="rgb-text">{{item.rgb}}</text>
          <text class="hex-text">{{item.hex}}</text>
        </view>
        <view class="color-percentages">
          <text class="percentage red">R: {{item.rPercent}}%</text>
          <text class="percentage green">G: {{item.gPercent}}%</text>
          <text class="percentage blue">B: {{item.bPercent}}%</text>
        </view>
        <text class="save-time">{{item.timeText}}</text>
      </view>
      <view class="color-actions">
        <button class="action-btn copy" bindtap="copyColor" data-hex="{{item.hex}}" catchtap="true">复制</button>
        <button class="action-btn edit" bindtap="editColor" data-color="{{item}}" catchtap="true">编辑</button>
        <button class="action-btn delete" bindtap="deleteColor" data-index="{{index}}" catchtap="true">删除</button>
      </view>
    </view>
  </view>

  <view class="empty-state" wx:else>
    <image src="/images/empty-colors.png" class="empty-image" mode="aspectFit"></image>
    <text class="empty-title">暂无保存的颜色</text>
    <text class="empty-subtitle">去拍照分析颜色并保存吧</text>
    <button class="btn primary" bindtap="goToIndex">开始使用</button>
  </view>

  <view class="bottom-actions" wx:if="{{colorList.length > 0}}">
    <button class="btn secondary" bindtap="clearAll">清空历史</button>
    <button class="btn primary" bindtap="goToIndex">继续分析</button>
  </view>
</view>
