/* pages/colorHistory/colorHistory.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.color-list {
  margin-bottom: 40rpx;
}

.color-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  gap: 30rpx;
}

.color-preview {
  width: 100rpx;
  height: 100rpx;
  border-radius: 15rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.color-details {
  flex: 1;
}

.color-values {
  margin-bottom: 15rpx;
}

.rgb-text {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.hex-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  font-family: 'Courier New', monospace;
}

.color-percentages {
  display: flex;
  gap: 20rpx;
  margin-bottom: 10rpx;
}

.percentage {
  font-size: 22rpx;
  font-weight: bold;
  padding: 5rpx 10rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
}

.percentage.red {
  color: #ff4757;
}

.percentage.green {
  color: #2ed573;
}

.percentage.blue {
  color: #3742fa;
}

.save-time {
  font-size: 20rpx;
  color: #999;
}

.color-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  flex-shrink: 0;
}

.action-btn {
  padding: 15rpx 25rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
  border: none;
  min-width: 80rpx;
}

.action-btn.copy {
  background: #3742fa;
  color: white;
}

.action-btn.edit {
  background: #2ed573;
  color: white;
}

.action-btn.delete {
  background: #ff4757;
  color: white;
}

.action-btn:active {
  opacity: 0.8;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  margin-bottom: 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.5;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.bottom-actions {
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.btn.primary {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
}

.btn.secondary {
  background: linear-gradient(45deg, #4834d4, #686de0);
  color: white;
}

.btn:active {
  opacity: 0.8;
}
