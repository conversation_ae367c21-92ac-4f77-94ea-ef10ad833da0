<!--pages/colorAnalysis/colorAnalysis.wxml-->
<view class="container">
  <view class="header">
    <text class="title">颜色调整</text>
    <text class="subtitle">实时调整RGB值查看颜色变化</text>
  </view>

  <!-- 原始颜色和调整后颜色对比 -->
  <view class="color-comparison">
    <view class="color-item">
      <view class="color-display original" style="background-color: rgb({{originalColor.r}}, {{originalColor.g}}, {{originalColor.b}});"></view>
      <text class="color-label">原始颜色</text>
      <text class="color-rgb">RGB({{originalColor.r}}, {{originalColor.g}}, {{originalColor.b}})</text>
    </view>
    
    <view class="color-item">
      <view class="color-display adjusted" style="background-color: rgb({{adjustedColor.r}}, {{adjustedColor.g}}, {{adjustedColor.b}});"></view>
      <text class="color-label">调整后颜色</text>
      <text class="color-rgb">RGB({{adjustedColor.r}}, {{adjustedColor.g}}, {{adjustedColor.b}})</text>
    </view>
  </view>

  <!-- RGB滑块控制 -->
  <view class="sliders-container">
    <view class="slider-group">
      <view class="slider-header">
        <text class="slider-title red">红色 (R)</text>
        <text class="slider-value">{{adjustedColor.r}}</text>
      </view>
      <slider 
        class="color-slider red-slider"
        min="0" 
        max="255" 
        value="{{adjustedColor.r}}" 
        bindchange="onRedChange"
        bindchanging="onRedChanging"
        activeColor="#ff4757"
        backgroundColor="#ddd"
        block-color="#ff4757"
        block-size="20"
      />
    </view>

    <view class="slider-group">
      <view class="slider-header">
        <text class="slider-title green">绿色 (G)</text>
        <text class="slider-value">{{adjustedColor.g}}</text>
      </view>
      <slider 
        class="color-slider green-slider"
        min="0" 
        max="255" 
        value="{{adjustedColor.g}}" 
        bindchange="onGreenChange"
        bindchanging="onGreenChanging"
        activeColor="#2ed573"
        backgroundColor="#ddd"
        block-color="#2ed573"
        block-size="20"
      />
    </view>

    <view class="slider-group">
      <view class="slider-header">
        <text class="slider-title blue">蓝色 (B)</text>
        <text class="slider-value">{{adjustedColor.b}}</text>
      </view>
      <slider 
        class="color-slider blue-slider"
        min="0" 
        max="255" 
        value="{{adjustedColor.b}}" 
        bindchange="onBlueChange"
        bindchanging="onBlueChanging"
        activeColor="#3742fa"
        backgroundColor="#ddd"
        block-color="#3742fa"
        block-size="20"
      />
    </view>
  </view>

  <!-- 颜色信息显示 -->
  <view class="color-info">
    <view class="info-section">
      <text class="info-title">三原色比例</text>
      <view class="percentage-bars">
        <view class="percentage-item">
          <text class="percentage-label red">红色</text>
          <view class="percentage-bar">
            <view class="percentage-fill red" style="width: {{adjustedColor.rPercent}}%;"></view>
          </view>
          <text class="percentage-value">{{adjustedColor.rPercent}}%</text>
        </view>
        
        <view class="percentage-item">
          <text class="percentage-label green">绿色</text>
          <view class="percentage-bar">
            <view class="percentage-fill green" style="width: {{adjustedColor.gPercent}}%;"></view>
          </view>
          <text class="percentage-value">{{adjustedColor.gPercent}}%</text>
        </view>
        
        <view class="percentage-item">
          <text class="percentage-label blue">蓝色</text>
          <view class="percentage-bar">
            <view class="percentage-fill blue" style="width: {{adjustedColor.bPercent}}%;"></view>
          </view>
          <text class="percentage-value">{{adjustedColor.bPercent}}%</text>
        </view>
      </view>
    </view>

    <view class="info-section">
      <text class="info-title">十六进制值</text>
      <text class="hex-value">{{hexValue}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="button-group">
    <button class="btn secondary" bindtap="resetColor">重置颜色</button>
    <button class="btn primary" bindtap="saveColor">保存颜色</button>
  </view>

  <!-- 隐藏的canvas用于颜色处理 -->
  <canvas canvas-id="hiddenCanvas" class="hidden-canvas"></canvas>
</view>
