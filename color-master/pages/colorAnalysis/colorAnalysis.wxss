/* pages/colorAnalysis/colorAnalysis.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.color-comparison {
  display: flex;
  justify-content: space-between;
  margin-bottom: 60rpx;
  gap: 30rpx;
}

.color-item {
  flex: 1;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.color-display {
  width: 100rpx;
  height: 100rpx;
  border-radius: 15rpx;
  margin: 0 auto 20rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.2);
}

.color-label {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.color-rgb {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.sliders-container {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.slider-group {
  margin-bottom: 50rpx;
}

.slider-group:last-child {
  margin-bottom: 0;
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.slider-title {
  font-size: 32rpx;
  font-weight: bold;
}

.slider-title.red {
  color: #ff4757;
}

.slider-title.green {
  color: #2ed573;
}

.slider-title.blue {
  color: #3742fa;
}

.slider-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  background: #f8f9fa;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}

.color-slider {
  width: 100%;
}

.color-info {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.info-section {
  margin-bottom: 40rpx;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.percentage-bars {
  space-y: 20rpx;
}

.percentage-item {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  gap: 20rpx;
}

.percentage-label {
  width: 80rpx;
  font-size: 26rpx;
  font-weight: bold;
}

.percentage-label.red {
  color: #ff4757;
}

.percentage-label.green {
  color: #2ed573;
}

.percentage-label.blue {
  color: #3742fa;
}

.percentage-bar {
  flex: 1;
  height: 20rpx;
  background: #f1f2f6;
  border-radius: 10rpx;
  overflow: hidden;
}

.percentage-fill {
  height: 100%;
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

.percentage-fill.red {
  background: linear-gradient(90deg, #ff4757, #ff3838);
}

.percentage-fill.green {
  background: linear-gradient(90deg, #2ed573, #1dd1a1);
}

.percentage-fill.blue {
  background: linear-gradient(90deg, #3742fa, #2f3542);
}

.percentage-value {
  width: 80rpx;
  text-align: right;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.hex-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
  text-align: center;
  font-family: 'Courier New', monospace;
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.btn.primary {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
}

.btn.secondary {
  background: linear-gradient(45deg, #4834d4, #686de0);
  color: white;
}

.btn:active {
  opacity: 0.8;
}

.hidden-canvas {
  position: fixed;
  top: -1000rpx;
  left: -1000rpx;
  width: 300rpx;
  height: 300rpx;
}
