// pages/colorAnalysis/colorAnalysis.js
Page({
  data: {
    originalColor: {
      r: 0,
      g: 0,
      b: 0,
      rPercent: 0,
      gPercent: 0,
      bPercent: 0
    },
    adjustedColor: {
      r: 0,
      g: 0,
      b: 0,
      rPercent: 0,
      gPercent: 0,
      bPercent: 0
    },
    hexValue: '#000000'
  },

  onLoad(options) {
    console.log('颜色分析页面加载', options)
    
    // 从参数获取初始颜色
    const r = parseInt(options.r) || 0
    const g = parseInt(options.g) || 0
    const b = parseInt(options.b) || 0
    
    const colorData = this.calculateColorData(r, g, b)
    
    this.setData({
      originalColor: colorData,
      adjustedColor: { ...colorData },
      hexValue: this.rgbToHex(r, g, b)
    })
  },

  // 计算颜色数据（RGB值和百分比）
  calculateColorData(r, g, b) {
    const total = r + g + b
    const rPercent = total > 0 ? Math.round((r / total) * 100) : 0
    const gPercent = total > 0 ? Math.round((g / total) * 100) : 0
    const bPercent = total > 0 ? Math.round((b / total) * 100) : 0
    
    return {
      r, g, b,
      rPercent, gPercent, bPercent
    }
  },

  // RGB转十六进制
  rgbToHex(r, g, b) {
    const toHex = (n) => {
      const hex = n.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase()
  },

  // 更新调整后的颜色
  updateAdjustedColor(r, g, b) {
    const colorData = this.calculateColorData(r, g, b)
    const hexValue = this.rgbToHex(r, g, b)
    
    this.setData({
      adjustedColor: colorData,
      hexValue: hexValue
    })
  },

  // 红色滑块变化（实时）
  onRedChanging(e) {
    const r = parseInt(e.detail.value)
    const { g, b } = this.data.adjustedColor
    this.updateAdjustedColor(r, g, b)
  },

  // 红色滑块变化完成
  onRedChange(e) {
    const r = parseInt(e.detail.value)
    const { g, b } = this.data.adjustedColor
    this.updateAdjustedColor(r, g, b)
    console.log('红色调整为:', r)
  },

  // 绿色滑块变化（实时）
  onGreenChanging(e) {
    const g = parseInt(e.detail.value)
    const { r, b } = this.data.adjustedColor
    this.updateAdjustedColor(r, g, b)
  },

  // 绿色滑块变化完成
  onGreenChange(e) {
    const g = parseInt(e.detail.value)
    const { r, b } = this.data.adjustedColor
    this.updateAdjustedColor(r, g, b)
    console.log('绿色调整为:', g)
  },

  // 蓝色滑块变化（实时）
  onBlueChanging(e) {
    const b = parseInt(e.detail.value)
    const { r, g } = this.data.adjustedColor
    this.updateAdjustedColor(r, g, b)
  },

  // 蓝色滑块变化完成
  onBlueChange(e) {
    const b = parseInt(e.detail.value)
    const { r, g } = this.data.adjustedColor
    this.updateAdjustedColor(r, g, b)
    console.log('蓝色调整为:', b)
  },

  // 重置颜色
  resetColor() {
    const { originalColor } = this.data
    this.setData({
      adjustedColor: { ...originalColor },
      hexValue: this.rgbToHex(originalColor.r, originalColor.g, originalColor.b)
    })
    
    wx.showToast({
      title: '已重置颜色',
      icon: 'success'
    })
  },

  // 保存颜色
  saveColor() {
    const { adjustedColor, hexValue } = this.data
    
    // 将颜色信息保存到本地存储
    const colorInfo = {
      rgb: `rgb(${adjustedColor.r}, ${adjustedColor.g}, ${adjustedColor.b})`,
      hex: hexValue,
      r: adjustedColor.r,
      g: adjustedColor.g,
      b: adjustedColor.b,
      rPercent: adjustedColor.rPercent,
      gPercent: adjustedColor.gPercent,
      bPercent: adjustedColor.bPercent,
      timestamp: Date.now()
    }
    
    // 获取已保存的颜色列表
    let savedColors = wx.getStorageSync('savedColors') || []
    savedColors.unshift(colorInfo)
    
    // 最多保存20个颜色
    if (savedColors.length > 20) {
      savedColors = savedColors.slice(0, 20)
    }
    
    wx.setStorageSync('savedColors', savedColors)
    
    // 复制十六进制值到剪贴板
    wx.setClipboardData({
      data: hexValue,
      success: () => {
        wx.showToast({
          title: '颜色已保存，十六进制值已复制',
          icon: 'success',
          duration: 2000
        })
      },
      fail: () => {
        wx.showToast({
          title: '颜色已保存',
          icon: 'success'
        })
      }
    })
    
    console.log('保存颜色:', colorInfo)
  },

  // 分享颜色
  onShareAppMessage() {
    const { adjustedColor, hexValue } = this.data
    return {
      title: `我调出了这个颜色 ${hexValue}`,
      path: `/pages/colorAnalysis/colorAnalysis?r=${adjustedColor.r}&g=${adjustedColor.g}&b=${adjustedColor.b}`,
      imageUrl: '' // 可以生成颜色预览图
    }
  }
})
