<!--index.wxml-->
<view class="container">
  <view class="header">
    <text class="title">颜色大师</text>
    <text class="subtitle">拍照分析颜色，调整三原色比例</text>
  </view>

  <view class="image-container" wx:if="{{selectedImage}}">
    <image src="{{selectedImage}}" mode="aspectFit" class="selected-image" bindtap="onImageTap"></image>
    <view class="tap-hint" wx:if="{{!colorSelected}}">点击图片选择颜色</view>
    <view class="color-point" wx:if="{{colorSelected}}" style="left: {{tapX}}px; top: {{tapY}}px;"></view>
  </view>

  <view class="color-info" wx:if="{{selectedColor}}">
    <view class="color-display" style="background-color: rgb({{selectedColor.r}}, {{selectedColor.g}}, {{selectedColor.b}});"></view>
    <view class="color-values">
      <text>RGB: ({{selectedColor.r}}, {{selectedColor.g}}, {{selectedColor.b}})</text>
      <text>红色比例: {{selectedColor.rPercent}}%</text>
      <text>绿色比例: {{selectedColor.gPercent}}%</text>
      <text>蓝色比例: {{selectedColor.bPercent}}%</text>
    </view>
  </view>

  <view class="button-group">
    <button class="btn primary" bindtap="chooseImage">
      {{selectedImage ? '重新选择图片' : '选择图片'}}
    </button>
    <button class="btn secondary" wx:if="{{selectedColor}}" bindtap="goToColorAnalysis">
      进入颜色调整
    </button>
    <button class="btn tertiary" bindtap="goToColorHistory">
      颜色历史
    </button>
  </view>

  <view class="instructions">
    <text class="instruction-title">使用说明：</text>
    <text>1. 点击"选择图片"拍照或从相册选择</text>
    <text>2. 点击图片上的任意位置选择颜色</text>
    <text>3. 查看颜色的RGB值和三原色比例</text>
    <text>4. 点击"进入颜色调整"进行实时调整</text>
  </view>

  <!-- 隐藏的canvas用于颜色提取 -->
  <canvas canvas-id="hiddenCanvas" class="hidden-canvas"></canvas>
</view>
