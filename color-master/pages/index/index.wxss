/* pages/index/index.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.image-container {
  position: relative;
  width: 100%;
  margin-bottom: 40rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.selected-image {
  width: 100%;
  height: 400rpx;
}

.tap-hint {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.color-point {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border: 4rpx solid white;
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.5);
  transform: translate(-50%, -50%);
}

.color-info {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.color-display {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  margin: 0 auto 30rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.2);
}

.color-values text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 10rpx;
  color: #333;
}

.button-group {
  margin-bottom: 60rpx;
}

.btn {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.btn.primary {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
}

.btn.secondary {
  background: linear-gradient(45deg, #4834d4, #686de0);
  color: white;
}

.btn.tertiary {
  background: linear-gradient(45deg, #2c2c54, #40407a);
  color: white;
}

.btn:active {
  opacity: 0.8;
}

.instructions {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 40rpx;
}

.instruction-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.instructions text:not(.instruction-title) {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  padding-left: 20rpx;
}

.hidden-canvas {
  position: fixed;
  top: -1000rpx;
  left: -1000rpx;
  width: 300rpx;
  height: 300rpx;
}
