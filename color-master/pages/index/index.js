// pages/index/index.js
Page({
  data: {
    selectedImage: '',
    selectedColor: null,
    colorSelected: false,
    tapX: 0,
    tapY: 0
  },

  onLoad() {
    console.log('首页加载完成')
  },

  // 选择图片
  chooseImage() {
    const that = this
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success(res) {
        const tempFilePath = res.tempFilePaths[0]
        that.setData({
          selectedImage: tempFilePath,
          selectedColor: null,
          colorSelected: false
        })
        console.log('图片选择成功:', tempFilePath)
      },
      fail(err) {
        console.error('图片选择失败:', err)
        wx.showToast({
          title: '图片选择失败',
          icon: 'none'
        })
      }
    })
  },

  // 点击图片获取颜色
  onImageTap(e) {
    if (!this.data.selectedImage) return
    
    const { x, y } = e.detail
    console.log('点击位置:', x, y)
    
    this.setData({
      tapX: x,
      tapY: y,
      colorSelected: true
    })
    
    this.getColorFromImage(x, y)
  },

  // 从图片获取颜色
  getColorFromImage(x, y) {
    const that = this
    const ctx = wx.createCanvasContext('colorCanvas', this)
    
    // 创建临时canvas来获取像素数据
    wx.createSelectorQuery()
      .select('.selected-image')
      .boundingClientRect(function(rect) {
        if (!rect) return
        
        // 计算在原图中的相对位置
        const scaleX = rect.width / rect.width
        const scaleY = rect.height / rect.height
        const imageX = Math.floor(x * scaleX)
        const imageY = Math.floor(y * scaleY)
        
        // 使用canvas获取像素数据
        that.extractColorFromCanvas(imageX, imageY)
      })
      .exec()
  },

  // 使用canvas提取颜色
  extractColorFromCanvas(x, y) {
    const that = this
    
    // 创建canvas上下文
    const ctx = wx.createCanvasContext('hiddenCanvas', this)
    
    // 绘制图片到canvas
    ctx.drawImage(this.data.selectedImage, 0, 0, 300, 300)
    ctx.draw(false, () => {
      // 获取像素数据
      wx.canvasGetImageData({
        canvasId: 'hiddenCanvas',
        x: Math.floor(x * 300 / 300), // 按比例缩放
        y: Math.floor(y * 300 / 300),
        width: 1,
        height: 1,
        success(res) {
          const data = res.data
          const r = data[0]
          const g = data[1] 
          const b = data[2]
          
          // 计算三原色比例
          const total = r + g + b
          const rPercent = total > 0 ? Math.round((r / total) * 100) : 0
          const gPercent = total > 0 ? Math.round((g / total) * 100) : 0
          const bPercent = total > 0 ? Math.round((b / total) * 100) : 0
          
          that.setData({
            selectedColor: {
              r, g, b,
              rPercent, gPercent, bPercent
            }
          })
          
          console.log('提取的颜色:', { r, g, b, rPercent, gPercent, bPercent })
        },
        fail(err) {
          console.error('获取像素数据失败:', err)
          // 备用方案：使用模拟数据
          that.simulateColorExtraction(x, y)
        }
      }, this)
    })
  },

  // 备用颜色提取方案（模拟）
  simulateColorExtraction(x, y) {
    // 基于点击位置生成模拟颜色
    const r = Math.floor((x / 300) * 255)
    const g = Math.floor((y / 300) * 255) 
    const b = Math.floor(((x + y) / 600) * 255)
    
    const total = r + g + b
    const rPercent = total > 0 ? Math.round((r / total) * 100) : 0
    const gPercent = total > 0 ? Math.round((g / total) * 100) : 0
    const bPercent = total > 0 ? Math.round((b / total) * 100) : 0
    
    this.setData({
      selectedColor: {
        r, g, b,
        rPercent, gPercent, bPercent
      }
    })
    
    wx.showToast({
      title: '颜色提取完成',
      icon: 'success'
    })
  },

  // 跳转到颜色调整页面
  goToColorAnalysis() {
    if (!this.data.selectedColor) return

    wx.navigateTo({
      url: `/pages/colorAnalysis/colorAnalysis?r=${this.data.selectedColor.r}&g=${this.data.selectedColor.g}&b=${this.data.selectedColor.b}`
    })
  },

  // 跳转到颜色历史页面
  goToColorHistory() {
    wx.navigateTo({
      url: '/pages/colorHistory/colorHistory'
    })
  }
})
